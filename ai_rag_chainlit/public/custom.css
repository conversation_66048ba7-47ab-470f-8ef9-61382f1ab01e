/* 自定义字体样式 - 解决中文字体显示问题 */

/* 导入中文字体 */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;700&display=swap');

/* 全局字体设置 */
* {
    font-family: 'Noto Sans SC', 'Inter', 'Microsoft YaHei', '微软雅黑', 'SimSun', '宋体', sans-serif !important;
}

/* 确保所有文本元素都使用正确的字体 */
body, html {
    font-family: 'Noto Sans SC', 'Inter', 'Microsoft YaHei', '微软雅黑', 'SimSun', '宋体', sans-serif !important;
    font-size: 14px;
    line-height: 1.6;
}

/* 消息内容字体 */
.MuiTypography-root {
    font-family: 'Noto Sans SC', 'Inter', 'Microsoft YaHei', '微软雅黑', 'SimSun', '宋体', sans-serif !important;
}

/* 输入框字体 */
input, textarea {
    font-family: 'Noto Sans SC', 'Inter', 'Microsoft YaHei', '微软雅黑', 'SimSun', '宋体', sans-serif !important;
}

/* 按钮字体 */
button {
    font-family: 'Noto Sans SC', 'Inter', 'Microsoft YaHei', '微软雅黑', 'SimSun', '宋体', sans-serif !important;
}

/* 标题字体 */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Noto Sans SC', 'Inter', 'Microsoft YaHei', '微软雅黑', 'SimSun', '宋体', sans-serif !important;
}

/* 聊天消息字体 */
.cl-message {
    font-family: 'Noto Sans SC', 'Inter', 'Microsoft YaHei', '微软雅黑', 'SimSun', '宋体', sans-serif !important;
}

/* 侧边栏字体 */
.cl-sidebar {
    font-family: 'Noto Sans SC', 'Inter', 'Microsoft YaHei', '微软雅黑', 'SimSun', '宋体', sans-serif !important;
}

/* 确保代码块也有合适的字体 */
code, pre {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace !important;
}

/* 修复可能的字体渲染问题 */
.MuiInputBase-root {
    font-family: 'Noto Sans SC', 'Inter', 'Microsoft YaHei', '微软雅黑', 'SimSun', '宋体', sans-serif !important;
}

.MuiButton-root {
    font-family: 'Noto Sans SC', 'Inter', 'Microsoft YaHei', '微软雅黑', 'SimSun', '宋体', sans-serif !important;
}

/* 确保所有Material-UI组件使用正确字体 */
.MuiTypography-body1,
.MuiTypography-body2,
.MuiTypography-subtitle1,
.MuiTypography-subtitle2,
.MuiTypography-caption {
    font-family: 'Noto Sans SC', 'Inter', 'Microsoft YaHei', '微软雅黑', 'SimSun', '宋体', sans-serif !important;
}

/* 聊天输入框特殊处理 */
.cl-input-wrapper input {
    font-family: 'Noto Sans SC', 'Inter', 'Microsoft YaHei', '微软雅黑', 'SimSun', '宋体', sans-serif !important;
    font-size: 14px !important;
}

/* 消息气泡字体 */
.cl-message-content {
    font-family: 'Noto Sans SC', 'Inter', 'Microsoft YaHei', '微软雅黑', 'SimSun', '宋体', sans-serif !important;
    font-size: 14px !important;
    line-height: 1.6 !important;
}

/* 确保Chainlit特定组件的字体 */
.cl-chat-container {
    font-family: 'Noto Sans SC', 'Inter', 'Microsoft YaHei', '微软雅黑', 'SimSun', '宋体', sans-serif !important;
}

.cl-header {
    font-family: 'Noto Sans SC', 'Inter', 'Microsoft YaHei', '微软雅黑', 'SimSun', '宋体', sans-serif !important;
}

/* 修复可能的字体加载问题 */
@font-face {
    font-family: 'Microsoft YaHei';
    src: local('Microsoft YaHei'), local('微软雅黑');
    font-display: swap;
}

@font-face {
    font-family: 'SimSun';
    src: local('SimSun'), local('宋体');
    font-display: swap;
}

/* 强制所有元素使用指定字体 */
[class*="Mui"], [class*="cl-"] {
    font-family: 'Noto Sans SC', 'Inter', 'Microsoft YaHei', '微软雅黑', 'SimSun', '宋体', sans-serif !important;
}

/* 特殊处理一些可能被遗漏的元素 */
div, span, p, a, label {
    font-family: 'Noto Sans SC', 'Inter', 'Microsoft YaHei', '微软雅黑', 'SimSun', '宋体', sans-serif !important;
}

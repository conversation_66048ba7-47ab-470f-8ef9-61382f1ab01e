[project]
# 是否启用遥测功能（默认：true）。不会收集任何个人数据。
enable_telemetry = true

# 需要每个用户提供的环境变量列表，用于使用该应用程序
user_env = []

# 连接丢失时保存会话的持续时间（以秒为单位）
session_timeout = 3600

# 启用第三方缓存（例如 LangChain 缓存）
cache = false

# 授权的来源列表，用于跨域资源共享（CORS）
allow_origins = ["*"]

# 跟随符号链接挂载资产（参见 https://github.com/Chainlit/chainlit/issues/317）
# follow_symlink = false

[features]
# 在消息中处理和显示 HTML。这可能存在安全风险（参见 https://stackoverflow.com/questions/19603097/why-is-it-dangerous-to-render-user-generated-html-or-javascript）
unsafe_allow_html = false

# 处理和显示数学表达式。这可能与消息中的 "$" 字符冲突
latex = false

# 自动使用当前聊天配置文件标记线程（如果使用聊天配置文件）
auto_tag_thread = true

# 允许用户编辑自己的消息
edit_message = true

# 授权用户自发上传文件与消息一起
[features.spontaneous_file_upload]
    # 是否启用文件上传功能
    enabled = true
    # 接受的文件类型，"*/*" 表示接受所有类型
    accept = ["*/*"]
    # 最大文件数量
    max_files = 20
    # 单个文件最大大小（以 MB 为单位）
    max_size_mb = 500

# 音频相关配置
[features.audio]
    # 音频录制的阈值
    min_decibels = -45
    # 用户开始说话前的延迟时间（毫秒）
    initial_silence_timeout = 3000
    # 用户继续说话的延迟时间（毫秒）。如果用户停止说话达到此持续时间，录音将停止
    silence_timeout = 1500
    # 录音的最大持续时间（毫秒），超过此时间将强制停止
    max_duration = 15000
    # 音频块的持续时间（毫秒）
    chunk_duration = 1000
    # 音频采样率
    sample_rate = 44100

[UI]
# 助手的名称，将显示在用户界面中
name = "印刷智能制造实验室"

# 助手的描述，用于 HTML 标签
# description = ""

# 默认情况下折叠大内容，以获得更清晰的用户界面
default_collapse_content = true

# 思维链（CoT）显示模式。可以是 "hidden"（隐藏）、"tool_call"（仅显示工具调用）或 "full"（完整显示）
cot = "full"

# GitHub 仓库链接，这将在用户界面的页眉中添加一个 GitHub 按钮
# github = ""

# 指定可用于自定义用户界面的 CSS 文件
# CSS 文件可以从 public 目录提供或通过外部链接提供
# custom_css = "/public/test.css"

# 指定可用于自定义用户界面的 JavaScript 文件
# JavaScript 文件可以从 public 目录提供
# custom_js = "/public/test.js"

# 指定自定义字体 URL
# custom_font = "https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap"

# 指定自定义元图像 URL
# custom_meta_image_url = "https://chainlit-cloud.s3.eu-west-3.amazonaws.com/logo/chainlit_banner.png"

# 为前端指定自定义构建目录
# 这可以用于自定义前端代码
# 注意：如果是相对路径，则不应以斜杠开头
# custom_build = "./public/build"

# 主题配置
[UI.theme]
    # 默认主题，可以是 "light"（浅色）或 "dark"（深色）
    default = "light"
    # 布局类型，可以是 "normal" 或 "wide"
    layout = "wide"
    # 字体族
    font_family = "Inter, sans-serif"
    
# 覆盖默认的 MUI 浅色主题（检查 theme.ts）
[UI.theme.light]
    # 背景颜色
    background = "#FAFAFA"
    # 纸张颜色（卡片、对话框等）
    paper = "#FFFFFF"

    # 浅色主题的主要颜色配置
    [UI.theme.light.primary]
        # 主要颜色
        main = "#F80061"
        # 深色变体
        dark = "#980039"
        # 浅色变体
        light = "#FFE7EB"
        
    # 浅色主题的文字颜色配置
    [UI.theme.light.text]
        # 主要文字颜色
        primary = "#212121"
        # 次要文字颜色
        secondary = "#616161"

# 覆盖默认的 MUI 深色主题（检查 theme.ts）
[UI.theme.dark]
    # 背景颜色
    background = "#FAFAFA"
    # 纸张颜色（卡片、对话框等）
    paper = "#FFFFFF"

    # 深色主题的主要颜色配置
    [UI.theme.dark.primary]
        # 主要颜色
        main = "#F80061"
        # 深色变体
        dark = "#980039"
        # 浅色变体
        light = "#FFE7EB"
        
    # 深色主题的文字颜色配置
    [UI.theme.dark.text]
        # 主要文字颜色
        primary = "#EEEEEE"
        # 次要文字颜色
        secondary = "#BDBDBD"

[meta]
# 生成该配置文件的 Chainlit 版本
generated_by = "1.3.2"